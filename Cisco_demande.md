PlantDetailScreen.tsx:100 Uncaught TypeError: Cannot read properties of null (reading 'toDate')
    at DiagnosticHistoryCard (PlantDetailScreen.tsx:100:69)
    at react-stack-bottom-frame (react-dom_client.js?v=417ca3a6:17424:20)
    at renderWithHooks (react-dom_client.js?v=417ca3a6:4206:24)
    at updateFunctionComponent (react-dom_client.js?v=417ca3a6:6619:21)
    at beginWork (react-dom_client.js?v=417ca3a6:7654:20)
    at runWithFiberInDEV (react-dom_client.js?v=417ca3a6:1485:72)
    at performUnitOfWork (react-dom_client.js?v=417ca3a6:10868:98)
    at workLoopSync (react-dom_client.js?v=417ca3a6:10728:43)
    at renderRootSync (react-dom_client.js?v=417ca3a6:10711:13)
    at performWorkOnRoot (react-dom_client.js?v=417ca3a6:10359:46)
