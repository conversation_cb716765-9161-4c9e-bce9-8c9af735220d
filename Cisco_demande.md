
Le lien calendrier dans la navigation fonctionne bien. J'ai pu accéder à la vue calendrier. 

Les couleurs sont harmonisées avec le thème sombre, oui. 




calendarSync.ts:96 Erreur lors de la synchronisation Firebase vers Google: FirebaseError: No document to update: projects/florasynth-a461d/databases/(default)/documents/users/nTfdEgyg0gP4GK2TnAOKNnI6flZ2/calendar_settings/settings
overrideMethod @ hook.js:608
syncFirebaseToGoogle @ calendarSync.ts:96
await in syncFirebaseToGoogle
fullSync @ calendarSync.ts:208
handleSync @ CalendarView.tsx:50
executeDispatch @ react-dom_client.js?v=4c04a682:11736
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
processDispatchQueue @ react-dom_client.js?v=4c04a682:11772
(anonymous) @ react-dom_client.js?v=4c04a682:12182
batchedUpdates$1 @ react-dom_client.js?v=4c04a682:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=4c04a682:11877
dispatchEvent @ react-dom_client.js?v=4c04a682:14792
dispatchDiscreteEvent @ react-dom_client.js?v=4c04a682:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
_c @ button.tsx:45
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateForwardRef @ react-dom_client.js?v=4c04a682:6461
beginWork @ react-dom_client.js?v=4c04a682:7864
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<Button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
CalendarView @ CalendarView.tsx:187
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateFunctionComponent @ react-dom_client.js?v=4c04a682:6619
beginWork @ react-dom_client.js?v=4c04a682:7654
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<...>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
App @ App.tsx:83
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateFunctionComponent @ react-dom_client.js?v=4c04a682:6619
beginWork @ react-dom_client.js?v=4c04a682:7654
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
(anonymous) @ index.tsx:14
calendarSync.ts:231 Erreur lors de la synchronisation complète: FirebaseError: No document to update: projects/florasynth-a461d/databases/(default)/documents/users/nTfdEgyg0gP4GK2TnAOKNnI6flZ2/calendar_settings/settings
overrideMethod @ hook.js:608
fullSync @ calendarSync.ts:231
await in fullSync
handleSync @ CalendarView.tsx:50
executeDispatch @ react-dom_client.js?v=4c04a682:11736
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
processDispatchQueue @ react-dom_client.js?v=4c04a682:11772
(anonymous) @ react-dom_client.js?v=4c04a682:12182
batchedUpdates$1 @ react-dom_client.js?v=4c04a682:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=4c04a682:11877
dispatchEvent @ react-dom_client.js?v=4c04a682:14792
dispatchDiscreteEvent @ react-dom_client.js?v=4c04a682:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
_c @ button.tsx:45
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateForwardRef @ react-dom_client.js?v=4c04a682:6461
beginWork @ react-dom_client.js?v=4c04a682:7864
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<Button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
CalendarView @ CalendarView.tsx:187
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateFunctionComponent @ react-dom_client.js?v=4c04a682:6619
beginWork @ react-dom_client.js?v=4c04a682:7654
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<...>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
App @ App.tsx:83
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateFunctionComponent @ react-dom_client.js?v=4c04a682:6619
beginWork @ react-dom_client.js?v=4c04a682:7654
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
(anonymous) @ index.tsx:14
api.ts:277  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=h1b9EDuDp0pCBGxDkPa1j9mC2tZ2BXaUmbXEwGlK1FE&SID=kaGSoc-BgNiKPpYluPWpzQ&RID=78960&TYPE=terminate&zx=vihc4nmoqryh 400 (Bad Request)
gc @ firebase_firestore.js?v=4c04a682:2147
Y2.close @ firebase_firestore.js?v=4c04a682:2491
(anonymous) @ firebase_firestore.js?v=4c04a682:12726
(anonymous) @ firebase_firestore.js?v=4c04a682:12690
ab @ firebase_firestore.js?v=4c04a682:950
F2 @ firebase_firestore.js?v=4c04a682:920
Z2.ta @ firebase_firestore.js?v=4c04a682:2540
Rb @ firebase_firestore.js?v=4c04a682:1419
M2.Y @ firebase_firestore.js?v=4c04a682:1284
M2.ca @ firebase_firestore.js?v=4c04a682:1215
ab @ firebase_firestore.js?v=4c04a682:950
F2 @ firebase_firestore.js?v=4c04a682:920
Wc @ firebase_firestore.js?v=4c04a682:1954
h.bb @ firebase_firestore.js?v=4c04a682:1949
h.Ea @ firebase_firestore.js?v=4c04a682:1946
Lc @ firebase_firestore.js?v=4c04a682:1846
h.Pa @ firebase_firestore.js?v=4c04a682:1813
Promise.then
Nc @ firebase_firestore.js?v=4c04a682:1804
h.Pa @ firebase_firestore.js?v=4c04a682:1814
Promise.then
Nc @ firebase_firestore.js?v=4c04a682:1804
h.Sa @ firebase_firestore.js?v=4c04a682:1800
Promise.then
h.send @ firebase_firestore.js?v=4c04a682:1781
h.ea @ firebase_firestore.js?v=4c04a682:1922
Jb @ firebase_firestore.js?v=4c04a682:1208
fd @ firebase_firestore.js?v=4c04a682:2341
h.Fa @ firebase_firestore.js?v=4c04a682:2308
Da @ firebase_firestore.js?v=4c04a682:669
Promise.then
x2 @ firebase_firestore.js?v=4c04a682:663
ec @ firebase_firestore.js?v=4c04a682:2294
Rb @ firebase_firestore.js?v=4c04a682:1416
M2.Y @ firebase_firestore.js?v=4c04a682:1284
M2.ca @ firebase_firestore.js?v=4c04a682:1215
ab @ firebase_firestore.js?v=4c04a682:950
F2 @ firebase_firestore.js?v=4c04a682:920
Wc @ firebase_firestore.js?v=4c04a682:1954
h.bb @ firebase_firestore.js?v=4c04a682:1949
h.Ea @ firebase_firestore.js?v=4c04a682:1946
Lc @ firebase_firestore.js?v=4c04a682:1846
h.Pa @ firebase_firestore.js?v=4c04a682:1813
Promise.then
Nc @ firebase_firestore.js?v=4c04a682:1804
h.Sa @ firebase_firestore.js?v=4c04a682:1800
Promise.then
h.send @ firebase_firestore.js?v=4c04a682:1781
h.ea @ firebase_firestore.js?v=4c04a682:1922
Jb @ firebase_firestore.js?v=4c04a682:1203
Hb @ firebase_firestore.js?v=4c04a682:1178
h.Ga @ firebase_firestore.js?v=4c04a682:2228
Da @ firebase_firestore.js?v=4c04a682:669
Promise.then
x2 @ firebase_firestore.js?v=4c04a682:663
fc @ firebase_firestore.js?v=4c04a682:2172
h.connect @ firebase_firestore.js?v=4c04a682:2132
Y2.m @ firebase_firestore.js?v=4c04a682:2488
Ho @ firebase_firestore.js?v=4c04a682:12684
send @ firebase_firestore.js?v=4c04a682:12575
k_ @ firebase_firestore.js?v=4c04a682:12892
na @ firebase_firestore.js?v=4c04a682:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=4c04a682:13447
(anonymous) @ firebase_firestore.js?v=4c04a682:12970
(anonymous) @ firebase_firestore.js?v=4c04a682:12994
(anonymous) @ firebase_firestore.js?v=4c04a682:16086
(anonymous) @ firebase_firestore.js?v=4c04a682:16117
Promise.then
uc @ firebase_firestore.js?v=4c04a682:16117
enqueue @ firebase_firestore.js?v=4c04a682:16086
enqueueAndForget @ firebase_firestore.js?v=4c04a682:16068
(anonymous) @ firebase_firestore.js?v=4c04a682:12994
(anonymous) @ firebase_firestore.js?v=4c04a682:12970
o_ @ firebase_firestore.js?v=4c04a682:12581
(anonymous) @ firebase_firestore.js?v=4c04a682:12732
setTimeout
P_ @ firebase_firestore.js?v=4c04a682:12731
z_ @ firebase_firestore.js?v=4c04a682:13074
W_ @ firebase_firestore.js?v=4c04a682:12967
(anonymous) @ firebase_firestore.js?v=4c04a682:12957
Promise.then
auth @ firebase_firestore.js?v=4c04a682:12953
start @ firebase_firestore.js?v=4c04a682:12852
start @ firebase_firestore.js?v=4c04a682:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=4c04a682:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=4c04a682:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_syncEngineWrite @ firebase_firestore.js?v=4c04a682:14446
await in __PRIVATE_syncEngineWrite
(anonymous) @ firebase_firestore.js?v=4c04a682:18100
await in (anonymous)
(anonymous) @ firebase_firestore.js?v=4c04a682:16086
(anonymous) @ firebase_firestore.js?v=4c04a682:16117
Promise.then
uc @ firebase_firestore.js?v=4c04a682:16117
enqueue @ firebase_firestore.js?v=4c04a682:16086
enqueueAndForget @ firebase_firestore.js?v=4c04a682:16068
__PRIVATE_firestoreClientWrite @ firebase_firestore.js?v=4c04a682:18100
executeWrite @ firebase_firestore.js?v=4c04a682:18101
updateDoc @ firebase_firestore.js?v=4c04a682:17942
createCalendarSettings @ api.ts:277
getOrCreateCalendarSettings @ api.ts:293
await in getOrCreateCalendarSettings
syncFirebaseToGoogle @ calendarSync.ts:33
fullSync @ calendarSync.ts:208
handleSync @ CalendarView.tsx:50
executeDispatch @ react-dom_client.js?v=4c04a682:11736
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
processDispatchQueue @ react-dom_client.js?v=4c04a682:11772
(anonymous) @ react-dom_client.js?v=4c04a682:12182
batchedUpdates$1 @ react-dom_client.js?v=4c04a682:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=4c04a682:11877
dispatchEvent @ react-dom_client.js?v=4c04a682:14792
dispatchDiscreteEvent @ react-dom_client.js?v=4c04a682:14773
<button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
_c @ button.tsx:45
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateForwardRef @ react-dom_client.js?v=4c04a682:6461
beginWork @ react-dom_client.js?v=4c04a682:7864
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<Button>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
CalendarView @ CalendarView.tsx:187
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateFunctionComponent @ react-dom_client.js?v=4c04a682:6619
beginWork @ react-dom_client.js?v=4c04a682:7654
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<...>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
App @ App.tsx:83
react-stack-bottom-frame @ react-dom_client.js?v=4c04a682:17424
renderWithHooksAgain @ react-dom_client.js?v=4c04a682:4281
renderWithHooks @ react-dom_client.js?v=4c04a682:4217
updateFunctionComponent @ react-dom_client.js?v=4c04a682:6619
beginWork @ react-dom_client.js?v=4c04a682:7654
runWithFiberInDEV @ react-dom_client.js?v=4c04a682:1485
performUnitOfWork @ react-dom_client.js?v=4c04a682:10868
workLoopSync @ react-dom_client.js?v=4c04a682:10728
renderRootSync @ react-dom_client.js?v=4c04a682:10711
performWorkOnRoot @ react-dom_client.js?v=4c04a682:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=4c04a682:11623
performWorkUntilDeadline @ react-dom_client.js?v=4c04a682:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=4c04a682:250
(anonymous) @ index.tsx:14























































